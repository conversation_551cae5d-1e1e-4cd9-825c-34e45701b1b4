---
id: developer_setup
title: Developer Setup
sidebar_label: Developer Setup
---

Here is how to setup dev tools before contributing to Querybook

## Before you start

To enable autocomplete and type checking, it is recommended to install all Querybook dependencies locally. However, you can also leverage VSCode's [Remote-Container](https://code.visualstudio.com/docs/remote/containers) plugin to develop directly inside the docker container (code changes will be auto-synced back with volumes).

If you do decide to develop locally, please make sure you have the following python and node versions available locally:

-   Python: ~3.10
-   Node: >=18

If you do not have the compatible version, we recommend the following methods to install the compatible versions required by Querybook.

-   Python: [use pyenv](https://github.com/pyenv/pyenv)
-   Node: [use nvm](https://github.com/nvm-sh/nvm)

## Setup Local DevTools

THIS PART IS IMPORTANT! This is required so that pre-commit hook can be used correctly.

```sh
git clone ...

cd querybook
python3 -m venv .virtualenv
source .virtualenv/bin/activate

pip install -r requirements.txt
pre-commit install
yarn install
```

## Running the frontend locally via webpack serve

Run the following command

```sh
yarn dev --env.QUERYBOOK_UPSTREAM=(Put your backend api server url here)
```

If you want to bypass cookie of upsteam do this

```sh
yarn dev --env.QUERYBOOK_UPSTREAM=(Put your backend api server url here) --env.QUERYBOOK_COOKIE=(Put backend api cookie here)
```
