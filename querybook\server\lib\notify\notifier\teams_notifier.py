## Geodis spec. This file was completely modified 

import requests
import logging
from env import QuerybookSettings
from lib.notify.base_notifier import BaseNotifier

LOG = logging.getLogger(__name__)

class TeamsNotifier(BaseNotifier):
    def __init__(self, webhook_url=None):
        self.webhook_url = webhook_url or QuerybookSettings.TEAMS_WEBHOOK_URL
        if not self.webhook_url:
            raise ValueError("Webhook URL must be provided")

    @property
    def notifier_name(self):
        return "Microsoft Teams"
    
    @property
    def notifier_help(self) -> str:
        return "Send notifications via Microsoft Teams webhook"
    
    @property
    def notifier_format(self):
        return "markdown"

    def notify_recipients(self, recipients, message, **kwargs):

        payload = {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "text": message
        }

        headers = {
            "Content-Type": "application/json"
        }

        try:
            response = requests.post(self.webhook_url, json=payload, headers=headers)
            response.raise_for_status()
            return True
        except requests.exceptions.RequestException as e:
            LOG.error(f"Failed to send notification: {e}")
            return False

    def notify(self, user, message):
        return self.notify_recipients(recipients=[], message=message)