import os
import uuid
from azure.storage.blob import BlobServiceClient
from lib.result_store.stores.base_store import BaseUploader, BaseReader
from typing import Generator
from azure.storage.blob import BlobType

def get_container_client():
    container_name = os.getenv('AZURE_STORAGE_CONTAINER_NAME')
    account_name = os.getenv('AZURE_STORAGE_ACCOUNT_NAME')
    account_key = os.getenv('AZURE_STORAGE_ACCOUNT_KEY')

    if not account_name or not account_key:
        raise ValueError("Azure Storage account name and key must be provided.")

    blob_service_client = BlobServiceClient(
        account_url=f"https://{account_name}.blob.core.windows.net",
        credential=account_key
    )
    return blob_service_client.get_container_client(container_name)

class AzureBlobUploader(BaseUploader):
    def __init__(self, key):
        account_name = os.getenv('AZURE_STORAGE_ACCOUNT_NAME')
        account_key = os.getenv('AZURE_STORAGE_ACCOUNT_KEY')
        container_name = os.getenv('AZURE_STORAGE_CONTAINER_NAME')

        if not account_name or not account_key:
            raise ValueError("Azure Storage account name and key must be provided.")

        blob_service_client = BlobServiceClient(
            account_url=f"https://{account_name}.blob.core.windows.net",
            credential=account_key
        )

        self.container_client = get_container_client()
        self.blob_name = key
        self.container_name = container_name
        self.blob_client = self.container_client.get_blob_client(self.blob_name)
        self.blob_service_client = blob_service_client

    def write(self, data):
        if isinstance(data, list):
            data = "\n".join(row.strip() for row in data)

        try:
            existing_data = self.blob_client.download_blob().readall().decode("utf-8")
        except Exception:
            existing_data = ""

        updated_data = existing_data + data
        self.blob_client.upload_blob(updated_data.encode("utf-8"), overwrite=True)
        return True

    def start(self):
        pass

    def end(self):
        pass

class AzureBlobReader(BaseReader):
    def __init__(self, key):
        account_name = os.getenv('AZURE_STORAGE_ACCOUNT_NAME')
        account_key = os.getenv('AZURE_STORAGE_ACCOUNT_KEY')
        container_name = os.getenv('AZURE_STORAGE_CONTAINER_NAME')

        if not account_name or not account_key:
            raise ValueError("Azure Storage account name and key must be provided.")

        self.blob_service_client = BlobServiceClient(
            account_url=f"https://{account_name}.blob.core.windows.net",
            credential=account_key
        )

        self.container_client = self.blob_service_client.get_container_client(container_name)
        self.blob_name = key
        self.blob_client = self.container_client.get_blob_client(self.blob_name)
        self.container_name = container_name

    def start(self):
        pass

    def end(self):
        pass

    def read_raw(self) -> bytes:
        try:
            blob_data = self.blob_client.download_blob()
            return blob_data.readall().decode("utf-8")
        except Exception as e:
            print(f"Error reading blob {self.blob_name}: {str(e)}")
            return ""

    def get_csv_iter(self, file_path=None):
        import csv
        from io import StringIO
        raw_data = self.read_raw()
        return csv.reader(StringIO(raw_data))

    def get_download_url(self) -> str:
        # Fallback to config-based function, in case it's needed elsewhere
        from lib.config import get_config_value
        return f"https://{get_config_value('AZURE_STORAGE_ACCOUNT_NAME')}.blob.{get_config_value('AZURE_STORAGE_ENDPOINT_SUFFIX')}/{self.container_name}/{self.blob_name}"

    def has_download_url(self) -> bool:
        return True

    def read_lines(self) -> Generator[str, None, None]:
        raw_data = self.read_raw()
        for line in raw_data.splitlines():
            yield line
