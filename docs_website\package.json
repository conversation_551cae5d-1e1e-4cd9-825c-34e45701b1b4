{"scripts": {"examples": "docusaurus-examples", "start": "docusaurus start", "build": "docusaurus build", "publish-gh-pages": "docusaurus-publish", "write-translations": "docusaurus-write-translations", "version": "docusaurus-version", "rename-version": "docusaurus-rename-version", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "serve": "docusaurus serve", "docusaurus": "<PERSON>cusaurus"}, "devDependencies": {}, "dependencies": {"@docusaurus/core": "^3.0.1", "@docusaurus/preset-classic": "^3.0.1", "body-scroll-lock": "^3.1.5", "clsx": "^1.1.1", "docusaurus-plugin-sass": "^0.2.5", "react": "^18.2.0", "react-dom": "^18.2.0", "sass": "^1.69.5"}}