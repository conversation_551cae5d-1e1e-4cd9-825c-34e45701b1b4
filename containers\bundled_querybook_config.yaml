# Make sure this is consistent with mysql part of docker-compose.yml
FLASK_SECRET_KEY: SOME_RANDOM_SECRET_KEY

DATABASE_CONN: mysql+pymysql://test:passw0rd@mysql:3306/querybook2?charset=utf8mb4
REDIS_URL: redis://redis:6379/0
ELASTICSEARCH_HOST: http://elasticsearch:9200
# ELASTICSEARCH_CONNECTION_TYPE: aws
# Uncomment for email
# EMAILER_CONN: dockerhostforward

# AI Assistant configuration for Groq
AI_ASSISTANT_PROVIDER: groq
AI_ASSISTANT_CONFIG:
    default:
        model_args:
            model_name: llama-3.3-70b-versatile
            temperature: 0
        reserved_tokens: 1024
    table_summary:
        model_args:
            model_name: llama-3.3-70b-versatile
            temperature: 0
    sql_summary:
        model_args:
            model_name: llama-3.3-70b-versatile
            temperature: 0
    table_select:
        model_args:
            model_name: llama-3.3-70b-versatile
            temperature: 0
    sql_complete:
        model_args:
            model_name: llama-3.3-70b-versatile
            temperature: 0

# Uncomment below to enable vector store to support embedding based table search.
# Please check langchain doc for the configs of each provider.
# EMBEDDINGS_PROVIDER: openai
# EMBEDDINGS_CONFIG: ~
# VECTOR_STORE_PROVIDER: opensearch
# VECTOR_STORE_CONFIG:
#     embeddings_arg_name: 'embedding_function'
#     opensearch_url: http://elasticsearch:9200
#     index_name: 'vector_index_v1'
