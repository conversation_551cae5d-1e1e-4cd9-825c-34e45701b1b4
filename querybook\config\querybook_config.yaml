# Please override by supply your own config here
# See docs/configurations/infra_config.md see how

# AI Assistant configuration for Groq
AI_ASSISTANT_PROVIDER: groq
AI_ASSISTANT_CONFIG:
    default:
        model_args:
            model_name: llama-3.3-70b-versatile
            temperature: 0
        reserved_tokens: 1024
    table_summary:
        model_args:
            model_name: llama-3.3-70b-versatile
            temperature: 0
    sql_summary:
        model_args:
            model_name: llama-3.3-70b-versatile
            temperature: 0
    table_select:
        model_args:
            model_name: llama-3.3-70b-versatile
            temperature: 0
    sql_complete:
        model_args:
            model_name: llama-3.3-70b-versatile
            temperature: 0
