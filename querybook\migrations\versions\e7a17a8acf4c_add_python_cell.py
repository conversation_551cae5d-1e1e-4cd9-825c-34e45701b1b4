"""add python cell

Revision ID: e7a17a8acf4c
Revises: d3582302cf48
Create Date: 2025-03-06 08:34:46.129355

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "e7a17a8acf4c"
down_revision = "d3582302cf48"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "data_cell",
        "cell_type",
        existing_type=sa.Enum("query", "text", "chart", name="datacelltype"),
        type_=sa.Enum("query", "text", "chart", "python", name="datacelltype"),
    )
    op.create_table(
        "python_cell_result",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("data_cell_id", sa.Integer(), nullable=False),
        sa.Column("output", sa.<PERSON>(), nullable=True),
#        sa.Column("error", sa.Text(length=65535), nullable=True),  #Geodis spec
        sa.Column("error", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(["data_cell_id"], ["data_cell.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("data_cell_id"),
        mysql_charset="utf8mb4",
        mysql_engine="InnoDB",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("python_cell_result")
    op.alter_column(
        "data_cell",
        "cell_type",
        existing_type=sa.Enum("query", "text", "chart", "python", name="datacelltype"),
        type_=sa.Enum("query", "text", "chart", name="datacelltype"),
    )
    # ### end Alembic commands ###
