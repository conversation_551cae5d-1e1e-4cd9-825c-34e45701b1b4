import os
from langchain_groq import ChatGroq
from langchain.schema import BaseLanguageModel

from lib.ai_assistant.base_ai_assistant import BaseAIAssistant
from lib.logger import get_logger

LOG = get_logger(__file__)


# Groq model context window sizes
GROQ_MODEL_CONTEXT_WINDOW_SIZE = {
    "llama-3.3-70b-versatile": 131072,  # 128k context window
    "llama-3.1-70b-versatile": 131072,  # 128k context window
    "llama-3.1-8b-instant": 131072,     # 128k context window
    "llama3-70b-8192": 8192,            # 8k context window
    "llama3-8b-8192": 8192,             # 8k context window
    "mixtral-8x7b-32768": 32768,        # 32k context window
    "gemma-7b-it": 8192,                # 8k context window
    "gemma2-9b-it": 8192,               # 8k context window
}
DEFAULT_MODEL_NAME = "llama-3.3-70b-versatile"


class GroqAssistant(BaseAIAssistant):
    """To use it, please set the following environment variable:
    GROQ_API_KEY: Groq API key
    """

    @property
    def name(self) -> str:
        return "groq"

    def _get_context_length_by_model(self, model_name: str) -> int:
        return (
            GROQ_MODEL_CONTEXT_WINDOW_SIZE.get(model_name)
            or GROQ_MODEL_CONTEXT_WINDOW_SIZE[DEFAULT_MODEL_NAME]
        )

    def _get_default_llm_config(self):
        default_config = super()._get_default_llm_config()
        if not default_config.get("model_name"):
            default_config["model_name"] = DEFAULT_MODEL_NAME

        return default_config

    def _get_token_count(self, ai_command: str, prompt: str) -> int:
        """
        Approximate token counting for Groq models.
        Since we don't have exact tokenizers for all Groq models,
        we use a rough approximation: 1 token ≈ 4 characters
        This is a conservative estimate that works reasonably well.
        """
        # Rough approximation: 1 token ≈ 4 characters
        # This is conservative and should work for most use cases
        return len(prompt) // 4

    def _get_error_msg(self, error) -> str:
        # Handle common Groq API errors
        error_str = str(error).lower()
        if "api key" in error_str or "unauthorized" in error_str:
            return "Invalid Groq API key. Please check your GROQ_API_KEY environment variable."
        elif "rate limit" in error_str:
            return "Groq API rate limit exceeded. Please try again later."
        elif "model not found" in error_str:
            return "Groq model not found. Please check your model configuration."
        
        return super()._get_error_msg(error)

    def _get_llm(self, ai_command: str, prompt_length: int) -> BaseLanguageModel:
        config = self._get_llm_config(ai_command)
        
        # Extract model name and other parameters
        model_name = config.get("model_name", DEFAULT_MODEL_NAME)
        temperature = config.get("temperature", 0)
        max_tokens = config.get("max_tokens", None)
        
        # Create ChatGroq instance
        llm_config = {
            "model": model_name,
            "temperature": temperature,
        }
        
        # Add max_tokens if specified
        if max_tokens:
            llm_config["max_tokens"] = max_tokens
            
        # Add API key if not set in environment
        api_key = '********************************************************'
        if api_key:
            llm_config["api_key"] = api_key
        
        return ChatGroq(**llm_config)
