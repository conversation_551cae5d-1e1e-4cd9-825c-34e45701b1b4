---
id: query_review
title: Query Review
sidebar_label: Query Review
---

## Overview

The Query Peer Review feature enables collaborative review of queries before execution, helping teams maintain quality standards and prevent potential issues.

:::note
To enable this feature, please refer to the [Query Review Integration Guide](../integrations/add_query_review.mdx).
:::

## Getting Started

### 1. Request a Review

When using a query engine with peer review enabled, you can submit your query for review before execution:

1. Write your query in the query editor
2. Click the dropdown menu on the right side
3. Select "Request Query Review" option

![Request Query Review Menu](/img/user_guide/query_review/query_review_1.png)

### 2. Submit Review Request

When the review request modal appears:

1. Select one or more appropriate reviewers
2. Provide a clear justification explaining the purpose of your query
3. Click "Submit" to send your request

![Review Request Form](/img/user_guide/query_review/query_review_2.png)

### 3. Track Review Status

After submission, you can monitor the status of all your review requests:

1. Navigate to the "Reviews" tab
2. View both reviews you've created and reviews assigned to you
3. Check the current status of each review (Pending, Approved, or Rejected)

![Reviews Tab](/img/user_guide/query_review/query_review_3.png)

### 4. Review Assigned Queries

When you're designated as a reviewer:

1. Go to the "Reviews" tab to see queries assigned to you
2. Review the query, its purpose, and potential impact
3. Choose to either approve the query (allowing execution) or reject it (requiring revision)
4. Optionally provide feedback to explain your decision

![Review Actions](/img/user_guide/query_review/query_review_4.png)

Once approved, the query will automatically execute, and the results will be available to the requester.
