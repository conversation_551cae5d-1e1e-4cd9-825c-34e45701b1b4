# --------------- Core Config ---------------
# Security key for flask, which is required to for any encryption with flask
FLASK_SECRET_KEY: ~
# Url of the querybook site, used for auth callback and notifications
PUBLIC_URL: ''
# Use this config to set cache policy of flask, see https://pythonhosted.org/Flask-Cache/ for details
FLASK_CACHE_CONFIG:
    CACHE_TYPE: 'simple'

# --------------- Celery ---------------
REDIS_URL: ~

# --------------- Search ---------------
ELASTICSEARCH_HOST: ~
ELASTICSEARCH_CONNECTION_TYPE: naive

# --------------- Lineage ---------------
DATA_LINEAGE_BACKEND: lib.lineage.db

# --------------- Database ---------------
DAT<PERSON><PERSON>E_CONN: ~
DATABASE_POOL_SIZE: 10
DATABASE_POOL_RECYCLE: 3600

# --------------- Communications ---------------
# Url to the email server
EMAILER_CONN: localhost
QUERYBOOK_SLACK_TOKEN: ~
QUERYBOOK_EMAIL_ADDRESS: ~

# --------------- Authentication ---------------
# Settings for auth type. Currently support 'app.auth.password_auth' which
# is authenticaton via username/password and 'app.auth.oauth_auth' which supports
# authentication via oauth api
AUTH_BACKEND: app.auth.password_auth
# Force user to log out after they log in for X number of seconds
# If set to 0 then only expire their login session after log out
LOGS_OUT_AFTER: 0

# If OAuth, then the following values should be provided
# Optional, if null then defaults to public url
OAUTH_CLIENT_ID: ~
OAUTH_CLIENT_SECRET: ~
OAUTH_AUTHORIZATION_URL: ~
OAUTH_TOKEN_URL: ~
OAUTH_USER_PROFILE: ~

# LDAP
LDAP_CONN: ~
LDAP_USER_DN: uid={},dc=example,dc=com
LDAP_UID_FIELD: uid
LDAP_EMAIL_FIELD: mail
LDAP_LASTNAME_FIELD: sn
LDAP_FIRSTNAME_FIELD: givenName
LDAP_FULLNAME_FIELD: cn

# Websocket CORS allowed origins
WS_CORS_ALLOWED_ORIGINS:
    - http://localhost:10001

# --------------- Result Store ---------------
RESULT_STORE_TYPE: db
# Geodis spec for using Azure blob storage
AZURE_STORAGE_ACCOUNT_NAME: ''
AZURE_STORAGE_ACCOUNT_KEY: ''
AZURE_STORAGE_ENDPOINT_SUFFIX: ''
AZURE_STORAGE_CONTAINER_NAME: ''


# Following settings are relevant to s3
STORE_BUCKET_NAME: ~
STORE_PATH_PREFIX: ''
STORE_MIN_UPLOAD_CHUNK_SIZE: ********
STORE_MAX_UPLOAD_CHUNK_NUM: 10000
STORE_MAX_READ_SIZE: 5242880
STORE_READ_SIZE: 131072
S3_BUCKET_S3V4_ENABLED: false
AWS_REGION: us-east-1

# Folowing settings are relevant to db store
DB_MAX_UPLOAD_SIZE: 5242880

# For Google service account Storage, also for querying
GOOGLE_CREDS: ~

# --------------- Logging ---------------
LOG_LOCATION: ~

# --------------- Table Upload (Experimental) ---------------
# Size in bytes
TABLE_MAX_UPLOAD_SIZE: ~

# --------------- Event Logging ---------------
EVENT_LOGGER_NAME: ~

# --------------- Stats Logging ---------------
STATS_LOGGER_NAME: ~

# --------------- AI Assistant ---------------
AI_ASSISTANT_PROVIDER: ~
AI_ASSISTANT_CONFIG:
    default:
        model_args:
            model_name: ~
            temperature: ~
            streaming: ~
        reserved_tokens: ~

EMBEDDINGS_PROVIDER: ~
EMBEDDINGS_CONFIG: ~
VECTOR_STORE_PROVIDER: ~
VECTOR_STORE_CONFIG:
    embeddings_arg_name: 'embedding_function'
    index_name: 'vector_index_v1'

# --------------- GitHub Integration ---------------
GITHUB_CLIENT_ID: ~
GITHUB_CLIENT_SECRET: ~
GITHUB_REPO_NAME: ~
GITHUB_BRANCH: 'main'
GITHUB_CRYPTO_SECRET: ''
