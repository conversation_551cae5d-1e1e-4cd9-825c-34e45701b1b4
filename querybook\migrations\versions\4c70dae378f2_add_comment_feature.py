"""add comment feature

Revision ID: 4c70dae378f2
Revises: 7f6cdb3621f7
Create Date: 2023-06-21 19:45:30.708378

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "4c70dae378f2"
down_revision = "7f6cdb3621f7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
#    MediumText = sa.Text(length=16777215)  #Geodis spec
    MediumText = sa.Text 
    conn = op.get_bind()
    if conn.dialect.name == "postgresql":
        MediumText = sa.Text()
    op.create_table(
        "comment",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("created_by", sa.Integer(), nullable=True),
        sa.Column("text", MediumText, nullable=True),
        sa.Column("parent_comment_id", sa.Integer(), nullable=True),
        sa.Column("archived", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(["created_by"], ["user.id"], ondelete="SET NULL"),
        sa.ForeignKeyConstraint(
            ["parent_comment_id"], ["comment.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
        mysql_charset="utf8mb4",
        mysql_engine="InnoDB",
    )
    op.create_table(
        "comment_reaction",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("comment_id", sa.Integer(), nullable=False),
        sa.Column("reaction", sa.String(length=255), nullable=False),
        sa.Column("created_by", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(["comment_id"], ["comment.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["created_by"], ["user.id"], ondelete="SET NULL"),
        sa.PrimaryKeyConstraint("id"),
        mysql_charset="utf8mb4",
        mysql_engine="InnoDB",
    )
    op.create_table(
        "data_cell_comment",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("data_cell_id", sa.Integer(), nullable=False),
        sa.Column("comment_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["comment_id"], ["comment.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["data_cell_id"], ["data_cell.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "data_table_comment",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("data_table_id", sa.Integer(), nullable=False),
        sa.Column("comment_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["comment_id"], ["comment.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["data_table_id"], ["data_table.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("data_table_comment")
    op.drop_table("data_cell_comment")
    op.drop_table("comment_reaction")
    op.drop_table("comment")
    # ### end Alembic commands ###
